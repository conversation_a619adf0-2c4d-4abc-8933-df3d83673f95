import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Flex,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  hubspot
} from "@hubspot/ui-extensions";

// Define the extension to be run within the HubSpot CRM
hubspot.extend(({ context, actions, runServerlessFunction }) =>
  <RMSContactCard context={context} actions={actions} runServerlessFunction={runServerlessFunction} />
);

const RMSContactCard = ({ context, actions, runServerlessFunction }) => {
  const [cardData, setCardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Extract context properties
  const {
    user,
    portal,
    crm: { objectId, objectType }
  } = context;

  // Get contact properties
  const contactProperties = context.crm?.objectProperties || {};
  const firstname = contactProperties.firstname || '';
  const lastname = contactProperties.lastname || '';
  const email = contactProperties.email || '';
  const phone = contactProperties.phone || '';

  // Load data from actual API via serverless function
  const loadRMSData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Prepare parameters for the API call
      const apiParams = {
        userId: user?.id || '',
        userEmail: user?.email || '',
        associatedObjectId: objectId || '',
        associatedObjectType: objectType || 'CONTACT',
        portalId: portal?.id || '',
        firstname: firstname,
        phone: phone,
        email: email,
        lastname: lastname
      };

      console.log('Calling RMS API with params:', apiParams);

      // Call the serverless function using runServerlessFunction
      const response = await runServerlessFunction({
        name: 'get-rms-data',
        parameters: apiParams
      });

      console.log('Serverless function response:', response);

      // Handle the response - try different response formats
      console.log('Raw response:', JSON.stringify(response, null, 2));

      let responseData = null;

      // Try different response formats based on HubSpot documentation
      if (response.status === 'SUCCESS' && response.response) {
        // Format 1: { status: 'SUCCESS', response: { body: { success: true, data: ... } } }
        const responseBody = response.response.body;
        if (responseBody?.success) {
          responseData = responseBody.data;
        } else {
          throw new Error(responseBody?.error || 'API returned unsuccessful response');
        }
      } else if (response.body?.success) {
        // Format 2: { body: { success: true, data: ... } }
        responseData = response.body.data;
      } else if (response.success) {
        // Format 3: { success: true, data: ... }
        responseData = response.data;
      } else if (response.status === 'SUCCESS') {
        // Format 4: Direct response
        responseData = response.response || response;
      } else {
        throw new Error(response.message || response.error || 'Serverless function failed');
      }

      if (responseData) {
        console.log('API data received:', responseData);
        setCardData(responseData);
      } else {
        throw new Error('No data received from API');
      }

    } catch (err) {
      console.error('Error loading RMS data:', err);

      // Fallback to mock data if API fails
      console.log('Falling back to mock data due to error:', err.message);

      const mockData = {
        results: [
          {
            title: "Setting",
            objectId: "964252",
            link: `https://api.niswey.net/rmscrmseries/wizard?portal_id=${portal?.id || '242859663'}&email=${encodeURIComponent(user?.email || '<EMAIL>')}&user_id=mock_user_id`,
            actions: [
              {
                type: "IFRAME",
                width: 1280,
                height: 900,
                uri: `https://api.niswey.net/rmscrmseries/initiate?portal_id=${portal?.id || '242859663'}&email=${encodeURIComponent(user?.email || '<EMAIL>')}&user_id=mock_user_id`,
                label: "See Mapping"
              }
            ]
          }
        ]
      };

      setCardData(mockData);
      // Don't set error state when falling back to mock data
      // setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadRMSData();
  }, []);

  // Handle "See Mapping" button click
  const handleSeeMappingClick = () => {
    if (cardData?.results?.[0]?.actions?.[0]) {
      const action = cardData.results[0].actions[0];

      if (action.type === 'IFRAME') {
        // Open iframe modal using HubSpot actions
        actions.openIframeModal({
          uri: action.uri,
          height: action.height || 900,
          width: action.width || 1280
        });
      }
    }
  };

  // Loading state
  if (loading) {
    return (
      <Card>
        <Flex direction="column" align="center" gap="medium">
          <LoadingSpinner />
          <Text>Loading RMS data...</Text>
        </Flex>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <Alert title="Error" variant="error">
          Failed to load RMS data: {error}
        </Alert>
        <Button
          onClick={loadRMSData}
          variant="primary"
          size="small"
        >
          Retry
        </Button>
      </Card>
    );
  }

  // No data state
  if (!cardData?.results?.[0]) {
    return (
      <Card>
        <Alert title="No Data" variant="warning">
          No RMS data available for this contact.
        </Alert>
      </Card>
    );
  }

  const result = cardData.results[0];
  const hasAction = result.actions?.[0];

  return (
    <Card>
      <Flex direction="column" gap="medium">
        {/* Header with title and external link icon */}
        <Flex justify="between" align="center">
          <Flex align="center" gap="small">
            <Text format={{ fontWeight: "bold", fontSize: "large" }}>
              {result.title}
            </Text>
            <Text>🔗</Text>
          </Flex>

          {/* Actions dropdown */}
          {hasAction && (
            <Flex direction="column" align="end">
              <Button
                variant="secondary"
                size="small"
                onClick={handleSeeMappingClick}
              >
                Actions ▼
              </Button>
            </Flex>
          )}
        </Flex>

        {/* Action button */}
        {hasAction && (
          <Flex justify="end">
            <Button
              variant="primary"
              size="medium"
              onClick={handleSeeMappingClick}
            >
              {result.actions[0].label}
            </Button>
          </Flex>
        )}

        {/* Footer */}
        <Flex justify="start">
          <Text format={{ fontSize: "small", color: "secondary" }}>
            Powered by RMS APP
          </Text>
        </Flex>
      </Flex>
    </Card>
  );
};
